<!DOCTYPE html><html dir="ltr" lang="en"><head><meta http-equiv="content-type" content="text/html; charset=utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="theme-color" content="#00affd" /><meta name="referrer" content="no-referrer" /><meta property="og:image" content="https://emailfake.com/mintext.jpg" /><title>Welcome to Augment Code - Temp Mail</title><link rel="dns-prefetch" href="//cdn.jsdelivr.net/"><link rel="dns-prefetch" href="//www.google-analytics.com/"><link rel="dns-prefetch" href="//pagead2.googlesyndication.com/"><link rel="alternate" hreflang="x-default" href="https://mail-temp.com/temp-mail-box/" /><link rel="alternate" hreflang="en" href="https://mail-temp.com/temp-mail-box/" /><link href="/css/top_v2.css" rel="stylesheet"></head><body><script data-ad-client="ca-pub-1407292178211259" async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script><script>(adsbygoogle = window.adsbygoogle || []).push({google_ad_client: "ca-pub-1407292178211259",enable_page_level_ads: true});</script><script>var gasmurl="/dichvuxe24h.com/sheilamaenaranjo";</script><script async src="https://www.googletagmanager.com/gtag/js?id=UA-35796116-33"></script>

<script>

  window.dataLayer = window.dataLayer || [];

  function gtag(){dataLayer.push(arguments);}

  gtag('js', new Date());

  gtag('config', 'UA-35796116-33');

  gtag('event', 'page_view', {

  page_title: window.location,

  page_location: 'dichvuxe24h.com/sheilamaenaranjo'

});

</script><div class="e7m lrwu5 site-header"><div class="e7m container"><div class="e7m row"><div class="e7m col-lg-12"><div class="e7m left-nav">



	<a class="e7m navbar-brand" href="/"><span style="font-size: x-large;"><span class="e7m logo_" style="color: #00a8ff;">Temp</span> <span class="e7m logo_" style="color: #5cb85c;">Mail</span></span></a><!--a class="e7m nav-link" href="/fake_email_generator"><span class="e7m nav1"></span><span class="e7m hidden-md-down">Generator</span></a-->

</div><div class="e7m righ-nav"> <span class="e7m "></span>

 <a class="e7m nav-link" href="/blog"><span class="e7m nav2"></span><span class="e7m hidden-md-down">Blog</span></a>

 <div class="e7m dropdown a_gre_tx">





  



  <span class="e7m nav4"></span>

 	<span class="e7m hidden-md-down">Recently email</span><div class="e7m dropdown-content"><div class="e7m dropdown-menu dropdown-menu-right daterangepicker opensleft"><a class="e7m dropdown-item waves-effect" href="/">There are no recently used mailboxes. Here you can see the latest mailboxes that you received email.</a></div></div></div></div></div></div></div></div>













<style>









.gform_wrapper.gform-wrapped {

    color: #51585e;

    padding: 30px;

    padding-top: 20px;

    background-color: #fff;

    border: none;

    -webkit-box-shadow: none;

    -moz-box-shadow: none;

    box-shadow: none;

    -webkit-box-shadow: none;

    -moz-box-shadow: none;

    box-shadow: none;

    -webkit-border-radius: 0px;

    -moz-border-radius: 0px;

    border-radius: 0px;

    -webkit-border-radius: 0px;

    -moz-border-radius: 0px;

    border-radius: 0px;

    background-color: #fff;

    background-color: #fff;

    margin-bottom: 30px;



}







.gform_title {

    color: #51585e;

    border-bottom: 1px solid #f2f2f2;

    -webkit-box-shadow: 0px 1px 1px 0px #fff;

    -moz-box-shadow: 0px 1px 1px 0px #fff;

    box-shadow: 0px 1px 1px 0px #fff;

    -webkit-box-shadow: 0px 1px 1px 0px #fff;

    -moz-box-shadow: 0px 1px 1px 0px #fff;

    box-shadow: 0px 1px 1px 0px #fff;

    padding-bottom: 10px;

    margin-top: 0px;



        margin: 12px 0;

    font-family: "Oswald", "Helvetica Neue", Helvetica, Arial, sans-serif;

    font-weight: 400;

    line-height: 24px;

    color: #425258;

    text-rendering: optimizelegibility;

}















</style>





<div class="e7m sinka" style="padding-top: 1px;

background-color: #4a88ac;

    background-image: url(/triangles-blue.png);">



<div class="e7m container">

  <h1 style="text-align: center;color: #fff;

    text-shadow: 2px 2px 2px rgba(0,0,0,0.3);

    padding-bottom: 10px;

    font-weight: 700;

    border-bottom: 1px solid #367498;

    -webkit-box-shadow: none;

    -moz-box-shadow: none;

    box-shadow: none;

    -webkit-box-shadow: none;

    -moz-box-shadow: none;

    box-shadow: none;">Temp Mail - Temporary Disposable email</h1>



  <div class="e7m row" style="z-index: 10">

    <div class="e7m col-md-6">



<div class="wpb_wrapper" style="color: #cae0df;">

   

<h3><span id="checkdomainset" style="padding-top: 0.5rem;">Checking email address...</span></h3>







<b><span id="email_ch_text"><EMAIL></span></b>





<br> have <span id="mess_number">1</span> message.<p>



<a href="/generate-temp-mail" rel="nofollow">

  <button type="button" class="e7m btn btn-success waves-effect waves-light waves-raised" style="padding: 5px 12px;">Generate new e-mail</button></a>





  <a href="/" id="refresh" rel="nofollow">

  <button type="button" class="e7m btn btn-success waves-effect waves-light waves-raised" style="padding: 5px 12px;">









<svg width="24px" height="24px" class="svg_spin_45rt" viewBox="0 0 128 128" xml:space="preserve"><path fill="#ffffff" fill-opacity="1" d="M109.25 55.5h-36l12-12a29.54 29.54 0 0 0-49.53 12H18.75A46.04 46.04 0 0 1 96.9 31.84l12.35-12.34v36zm-90.5 17h36l-12 12a29.54 29.54 0 0 0 49.53-12h16.97A46.04 46.04 0 0 1 31.1 96.16L18.74 108.5v-36z"></path></svg><span style="padding-left:30px;">Refresh</span></button></a>







</p>







    </div>







      </div>

    <div class="e7m col-md-6">























      



<div class="gf_browser_chrome gform_wrapper gform-wrapped row-fluid_wrapper" style="border:4px dashed #4a88ac;">



                        



                      

                         



                        <h4 class="gform_title">

                            <span class="e7m nav4"></span> Temp mail editor (search/write/add)</h4>                                                

                          Email Address





                  <div class="e7m coserch"><input type="text" spellcheck="false" maxlength="25" class="e7m form-control2 coselect" id="userName" onchange="change_username()" value="sheilamaenaranjo"><div class="e7m input-group-addon1 coselect2">@</div><div id="domnamserch666" class="e7m twitter-typeahead222" style="position: relative; direction: ltr;"><input type="text" spellcheck="false" class="e7m form-control2 typeahead_as3gsdr coselect3 tt-query s13" autocomplete="off" value="dichvuxe24h.com" id="domainName2" maxlength="59" onchange="change_write_domain();" dir="auto" style="padding-left: 0;"><div id="newselect" class="e7m tt-dropdown-menu hide_all" style="position: absolute; top: 100%; left: 0px; z-index: 10; display: block; right: auto; margin: .3rem -.6rem 0rem;"><div class="tt-dataset-typeahead_as3gsdr" style="display: block; overflow: hidden;"><div class="e7m tt-suggestions" style="display: block;"><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="vaynhanh2k7.com" style="white-space: normal;">vaynhanh2k7.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="skyfieldhub.com" style="white-space: normal;">skyfieldhub.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="boranora.com" style="white-space: normal;">boranora.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="techspirehub.com" style="white-space: normal;">techspirehub.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="cabangnursalina.net" style="white-space: normal;">cabangnursalina.net</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="cggup.com" style="white-space: normal;">cggup.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="incompetentgracia.net" style="white-space: normal;">incompetentgracia.net</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="zohoseek.com" style="white-space: normal;">zohoseek.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="iptakedownusa.com" style="white-space: normal;">iptakedownusa.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="naverly.com" style="white-space: normal;">naverly.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="theanseladams.com" style="white-space: normal;">theanseladams.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="dichvumxh247.top" style="white-space: normal;">dichvumxh247.top</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="tignovate.com" style="white-space: normal;">tignovate.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="newbreedapps.com" style="white-space: normal;">newbreedapps.com</p></div><div class="e7m tt-suggestion" style="white-space: nowrap; cursor: pointer;"><p onclick="change_dropdown_list(this.innerHTML)" id="basebuykey.com" style="white-space: normal;">basebuykey.com</p></div></div></div></div>





</div><div  style="background-color: #2d92d9;border-color: #2d92d9;" class="e7m dropselect waves-effect waves-light waves-raised" onclick="toggleClass(document.getElementById('newselect'), 'hide_all');"></div><button id="copbtn" type="button" class="e7m btn btn-success waves-effect waves-light waves-raised" style="background-color: #2d92d9;border-color: #2d92d9;margin-left: 10px;padding: 5px 12px;" onclick="copyToClipboard('#email_ch_text')">Copy</button></div>









                           





      



      </div>



    </div>









    </div></div>





  </div>

































<div class="e7m container to1"><div class="e7m row"><div class="e7m col-lg-12 s16"><div class="e7m list-group"><div class="e7m list-group-item active"><div class="e7m from_div_45g45gg bold_tyy90">From</div><div class="e7m subj_div_45g45gg bold_tyy90">Subject</div><div class="e7m time_div_45g45gg bold_tyy90">Time (UTC)</div></div><div id="email-table"><div class="e7m list-group-item list-group-item-info"><div class="e7m from_div_45g45gg"><EMAIL></div><div class="e7m subj_div_45g45gg">Welcome to Augment Code</div><div class="e7m time_div_45g45gg">2025-07-30 23:00:02</div></div><div class="e7m list-group-item"><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-1407292178211259" data-ad-slot="8722298763" data-ad-format="auto"></ins><script>(adsbygoogle = window.adsbygoogle || []).push({});</script></div><div class="e7m row list-group-item" style="margin-right: 0px;  margin-left: 0px;">

<div class="e7m col-md-9" style="display: inline-block;"><span>To: </span><span><EMAIL></span><br><span>From: </span><span><EMAIL><span style="display: inline; font-size: 13px; color: #3498db;"> <a href="//myip-address.com/ip-lookup/e226-12.smtp-out.us-east-2.amazonses.com/?email=34z2l5l4t5m4p4q5k2n4l4w5l4f4r4o4b3i5y5e4t574t506l5u5h5741444x5v5" target="_blank">(sender info)</a></span></span><br><span>Subject: </span><div style="display: inline; word-wrap: break-word; overflow-wrap: break-word;"><h1 style="font-size:1.1rem;font-weight:100;padding:0;margin:0;line-height:0;display:inherit;">Welcome to Augment Code</h1></div><br><span>Received: </span><span>2025-07-30 23:00:02<span class="e7m has-tooltip" style="display: inline"><span class="e7m secit45e hoverbubble" style="display: inline; font-size: 10px; color: #808080;"> (3 sec.)</span>

   <span class="e7m tooltip">Created: 2025-07-30 22:59:59</span>

</span></span></div><div class="e7m col-md-3" style="display: inline-block;"><button class="e7m btn btn-danger waves-effect waves-raised ma-2" type="button" value="Delete Message" onclick="Delete_Message()">Delete Message</button></div><div class="e7m col-md-12"><ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-1407292178211259" data-ad-slot="2675765164" data-ad-format="link"></ins><script>(adsbygoogle = window.adsbygoogle || []).push({});</script></div><div class="e7m col-md-12 ma1" style="padding-right: 0rem; padding-left: 0rem;"><div class="e7m border-top"></div><div class="e7m border-left"></div><div class="e7m mess_bodiyy">
  
    
    
  
  <center>
  <table style="width: 600px; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin: 0; padding: 0; font-family: &quot;ProximaNova&quot;, sans-serif; border-collapse: collapse !important; height: 100% !important" align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
    <tr>
      <td align="center" valign="top" id="bodyCell" style="-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; mso-table-lspace: 0pt; mso-table-rspace: 0pt; margin: 0; padding: 20px; font-family: &quot;ProximaNova&quot;, sans-serif; height: 100% !important">
      <div class="main">
        <p style="text-align: center; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; margin-bottom: 30px">
          <img src="https://www.augmentcode.com/android-chrome-512x512.png" width="50" alt="Your logo goes here" style="-ms-interpolation-mode: bicubic; border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none" />
        </p>

        
        

          
          

            <p style="font-size: 1.4em; line-height: 1.3">Your verification code is: <b>068878</b></p>

          

        

        <p style="-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%">If you are having any issues with your account, please don't hesitate to contact us by replying to this mail.</p>

        <br />
        Thanks!
        <br />

        <strong>Augment Code</strong>

        <br /><br />
        <hr style="border: 2px solid #EAEEF3; border-bottom: 0; margin: 20px 0" />
        <p style="text-align: center; color: #A9B3BC; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%">
          If you did not make this request, you can safely ignore this email. Never share this one-time code with anyone - Augment support will never ask for your verification code. Your account remains secure and no action is needed.
        </p>
      </div>
      </td>
    </tr>
  </table>
</center>

</div><div class="e7m border-right"></div><div class="e7m border-bottom"></div></div></div></div></div></div></div></div>





<script src="/js/sum25v01y21.js"></script>

<script type="text/javaScript">

var useridis = document.getElementById('userName').value;var domainis = document.getElementById('domainName2').value;var smurl=domainis.toLowerCase()+"/"+useridis.replace(/[^a-zA-Z\_0-9\.-]/g, '').toLowerCase();document.cookie="surl="+smurl+"/97f893ede6718fcc2ad42680cb8e36a2"+"; expires=**********+86400; path=/;domain=.mail-temp.com;";var channel16=(useridis+'@dichvuxe24h.com').toLowerCase();



//var socket = io.connect('//mail-temp.com');



var socket = io('wss://mail-temp.com',{

        path: '/socket.io',

        transports: ['websocket']

    });



socket.on("new_email", function (data) {/*console.log(data)*/;document.getElementById('mess_number').innerHTML=Number(document.getElementById('mess_number').innerHTML)+Number(1);var newmsg = JSON.parse(data);rec_offline=newmsg.recieved;$("#email-table").prepend("<a href=\""+newmsg.clickgo+"\" class=\"e7m list-group-item waves-effect list-group-item-success\">"+newmsg.tddata+"</a>");});socket.emit('watch_for_my_email', channel16);</script><script type="text/javascript">$(document).ready(function (){$('input.typeahead_as3gsdr').typeahead({ name: 'typeahead_as3gsdr', remote:'/search.php?key=%QUERY' , hint: "false", limit : 15 });});function Delete_Message(){jQuery.post( "//mail-temp.com/del_mail.php", { delll: "v2c4d4z2c433d4y344d434o5r4l4x494x294l5t2j5h4s50484b444z2g5l216q584f4d4i5v5h5c4w514o4h5h4s5n4r2b4v2a4w2d4w254d49413c42433m554i564j5j554s294x264l554236484c4h5x22454j4h5c4u5a4m4i5p4o2q294z234t2s2"}).done(function( data ) {/*console.log(data);*/window.location.href = "/dichvuxe24h.com/sheilamaenaranjo"; });}

var triger_recon=true;

setInterval(function() { 

if (triger_recon && socket.id) {

socket.emit('watch_for_my_email', channel16);

triger_recon=false;

}if (socket.id==undefined) {

triger_recon=true;

}},1000);



var rec_offline="d41d8cd98f00b204e9800998ecf8427e";jQuery.post( "//mail-temp.com/del_mail.php", { recieved: "v2c4d4z2c433d4y344d434o5r4l4x494x294l5t2j5h4s50484b444z2g5l216q584f4d4i5v5h5c4w514o4h5h4s5n4r2b4v2a4w2d4w254d49413c42433m554i564j5j554s294x264l554236484c4h5x22454j4h5c4u5a4m4i5p4o2q294z234t2s2"}).done(function( data ) { rec_offline=data; });socket.on('reconnect', function (data) {socket.emit('watch_for_my_email', channel16);console.log("chanell reconnect");jQuery.post( "//mail-temp.com/del_mail.php", { recieved: "v2c4d4z2c433d4y344d434o5r4l4x494x294l5t2j5h4s50484b444z2g5l216q584f4d4i5v5h5c4w514o4h5h4s5n4r2b4v2a4w2d4w254d49413c42433m554i564j5j554s294x264l554236484c4h5x22454j4h5c4u5a4m4i5p4o2q294z234t2s2"}).done(function( data ){ if (data != rec_offline) {window.location.href=smurl;}});});function adresss_valids(){document.getElementById("checkdomainset").innerHTML ="Checking email address...";$.post( "//mail-temp.com/check_adres_validation3.php", { usr: useridis, dmn: domainis }).done(function( data ){stas=JSON.parse(data); if (stas.status === "good") {smurl=domainis.toLowerCase()+"/"+useridis.replace(/[^a-zA-Z\_0-9\.-]/g, '').toLowerCase();document.cookie="surl="+smurl+"; expires=**********+86400; path=/;domain=.mail-temp.com;";document.getElementById("checkdomainset").className = "greenclass56fgc"; if (stas.uptime=="1") {days="day";} else {days="days";}  document.getElementById("checkdomainset").innerHTML ="Address is valid (uptime "+stas.uptime+" "+days+")";};  if (stas.status==="bad") {document.getElementById("checkdomainset").className = "redclass56fgc";   document.getElementById("checkdomainset").innerHTML ="Address is not valid";document.cookie="surl=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;domain=.mail-temp.com;";}; if (stas.status !== "bad" && stas.status !== "good") { document.getElementById("checkdomainset").innerHTML ="temporary error";};});}adresss_valids();var domain = document.getElementById('domainName2').value;function change_username() {clipboard_process(document.getElementById('userName').value,domainis);}function change_select_domain() {clipboard_process(document.getElementById('userName').value,document.getElementById('domainName').value);}function change_write_domain() {clipboard_process(document.getElementById('userName').value,document.getElementById('domainName2').value);}$(document).mouseup(function(e){var container = $('newselect'); if (!container.is(e.target) && container.has(e.target).length === 0){        removeClass(document.getElementById('newselect'), 'hide_all');}});function change_dropdown_list(p_value) {removeClass(document.getElementById('newselect'), 'hide_all');clipboard_process(document.getElementById('userName').value,p_value);}function SelectText(element){var doc=document,text=doc.getElementById(element),range,selection;if (doc.body.createTextRange) {range = document.body.createTextRange();range.moveToElementText(text);range.select();} else if (window.getSelection) {selection = window.getSelection();range = document.createRange();range.selectNodeContents(text);selection.removeAllRanges();selection.addRange(range);}}document.onclick = function(e) { if (e.target.id === 'copbtn') {SelectText('email_ch_text');}if (e.target.id === 'email_ch_text') {SelectText('email_ch_text');}};function clipboard_process(userid,domain){userid=char_validator(userid);document.getElementById("userName").value = userid;domain=dom_validator(domain);document.getElementById("domainName2").value = domain;socket.emit('dont_watch', channel16);jQuery.post( "/dom_to_punycode.php", { dmn: domain } ).done(function( data ) {var domain_chnnn='';var chanelident = (userid +'@'+ data).toLowerCase();socket.emit('watch_for_my_email', chanelident);channel16=chanelident;});var cureny_email = userid +'@'+ domain;document.getElementById("email_ch_text").innerHTML = cureny_email;smurl=domain.toLowerCase()+"/"+userid.replace(/[^a-zA-Z\_0-9\.-]/g, '').toLowerCase();document.cookie="surl="+smurl+"; expires=**********+86400; path=/;domain=.mail-temp.com;";window.location.href=smurl; useridis = userid;domainis = domain;adresss_valids();document.getElementById("refresh").href=smurl;}function char_validator(text) {text=text.replace(/[^a-zA-Z\_0-9\.-]|\_\.|\.\_|\_\-|\-\_|\-\.|\.\-|\.$|-$|^\.|^-|^\_|\_$/g, '').replace(/\.\.+/g, '.').replace(/--+/g, '-').replace(/__+/g, '_').replace(/[^a-zA-Z\_0-9\.-]|\_\.|\.\_|\_\-|\-\_|\-\.|\.\-|\.$|-$|^\.|^-|^\_|\_$/g, '');return text;}function dom_validator(text) {text=text.replace(/\.\.+/g, '.').replace(/---+/g, '--').replace(/__+/g, '_').replace(/ |\_\.|\.\_|\_\-|\-\_|\-\.|\.\-|\.$|-$|^\.|^-|^\_|\_$/g, '');return text;}function toggleClass(element, className){if (!element || !className){return;}var classString = element.className, nameIndex = classString.indexOf(className);if (nameIndex == -1) {classString += ' ' + className;} else {classString = classString.substr(0, nameIndex) + classString.substr(nameIndex+className.length); } element.className = classString;}function removeClass(element, className){ if (!element || !className){return;} var classString = element.className, nameIndex = classString.indexOf(className); if (nameIndex == -1) {classString+=' '+className;}element.className=classString;}document.getElementById('domainName2').addEventListener('click', function(){removeClass(document.getElementById('newselect'), 'hide_all');});function copyToClipboard(e){var t,o=$(e),c=!0,a=document.createRange();if(window.clipboardData)window.clipboardData.setData("Text",o.text());else{var n=$("<div>");n.css({position:"absolute",left:"-1000px",top:"-1000px"}),n.text(o.text()),$("body").append(n),a.selectNodeContents(n.get(0)),(t=window.getSelection()).removeAllRanges(),t.addRange(a);try{c=document.execCommand("copy",!1,null)}catch(e){console.log(o.text())}c&&(console.log(o.text()),n.remove())}}!function(){"use strict";function e(e){var t=e.target,o=t.dataset.copytarget,c=o?document.querySelector(o):null;if(c&&c.select){c.select();try{document.execCommand("copy"),c.blur(),t.classList.add("copied"),setTimeout(function(){t.classList.remove("copied")},1500)}catch(e){alert("please press Ctrl/Cmd+C to copy")}}}document.body.addEventListener("click",e,!0)}();document.getElementById("refresh").href=smurl;</script>

  <!--script src="https://cdn.jsdelivr.net/gh/emailfake/cdn@1.09/js/wavesv2.js" integrity="sha384-24Q2JMx7ghRJhv5zaHWdgPwJHq/YtK5JuCJwjG/SF+SxKJ5wVWYSnVZ28+tlIi7x" crossorigin="anonymous"></script><script>window.Waves || document.write('<script src="/js/wavesv2.js"><\/script>');</script--><div class="e7m container"><div class="e7m row"><div class="e7m col-lg-12"><div class="e7m footer_wrap"><span class="e7m text-muted" onclick='this.innerHTML = "<a"+" hr"+"ef=\"mai"+"lto"+":fee"+"db"+"ack"+"@m"+"a"+"i"+"l"+"-"+"t"+"e"+"m"+"p"+"."+"c"+"o"+"m"+"\">"+"fe"+"ed"+"bac"+"k@m"+"a"+"i"+"l"+"-"+"t"+"e"+"m"+"p"+"."+"c"+"o"+"m"+"</a>";'>Feedback</span><span class="e7m text-muted"> | </span><a href="/blog" class="e7m text-muted">Blog</a></div></div></div></div><script type="text/javaScript">function loadmycss(){var cssId2 = 'myCss2';if (!document.getElementById(cssId2)){var head  = document.getElementsByTagName('head')[0];var link  = document.createElement('link');link.id=cssId2;link.rel='stylesheet';link.type='text/css';link.href = 'https://mail-temp.com/css/last_v2.css';link.media = 'all';head.appendChild(link);}}</script><link onerror='loadmycss();' href="https://cdn.jsdelivr.net/gh/emailfake/cdn@1.15/css/last_v2.css" rel="stylesheet" integrity="sha384-FFxMiP8fXMjRx1WfwHOk7Qs9+Fwkf6leVbAWPg+4CUoMfV0otNqocsVH/C7ofZ5Q" crossorigin="anonymous"></body></html>