import requests
import json
from bs4 import BeautifulSoup
import re

def get_temp_mail_data(url):
    """
    访问临时邮箱网站并解析返回的数据
    """
    try:
        # 发送GET请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        
        # 检查响应状态码
        if response.status_code == 200:
            print(f"请求成功，状态码: {response.status_code}")
            
            # 尝试直接解析JSON
            try:
                json_data = response.json()
                print("成功解析JSON数据:")
                print(json.dumps(json_data, ensure_ascii=False, indent=2))
                return json_data
            except json.JSONDecodeError:
                print("响应内容不是直接的JSON格式，尝试从HTML中提取...")
                
                # 尝试从HTML中提取JSON数据
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 打印HTML内容以便调试
                print("HTML内容:")
                print(response.text[:1000])  # 只打印前1000个字符避免过多输出
                
                # 尝试查找可能包含JSON的script标签
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and '{' in script.string:
                        # 使用正则表达式尝试提取JSON对象
                        json_matches = re.findall(r'(\{.*?\})', script.string, re.DOTALL)
                        for potential_json in json_matches:
                            try:
                                data = json.loads(potential_json)
                                print("从脚本标签中提取的JSON数据:")
                                print(json.dumps(data, ensure_ascii=False, indent=2))
                                return data
                            except:
                                continue
                
                # 如果没有在script标签中找到，尝试查找API响应
                api_data = soup.find('pre')
                if api_data and api_data.string:
                    try:
                        data = json.loads(api_data.string)
                        print("从pre标签中提取的JSON数据:")
                        print(json.dumps(data, ensure_ascii=False, indent=2))
                        return data
                    except:
                        pass
                        
                print("无法从响应中提取JSON数据")
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

if __name__ == "__main__":
    url = "https://mail-temp.com/temp-mail-box/dichvuxe24h.com/sheilamaenaranjo"
    print(f"正在访问: {url}")
    get_temp_mail_data(url)
